"""
Robust PDF Processing Service

This module provides comprehensive error handling and validation for PDF processing
with multiple fallback strategies and retry logic.
"""

import os
import time
import logging
from typing import Dict, List, Any, Optional
import fitz  # PyMuPDF
from app.utils.performance_monitor import performance_monitor
from app.services.pdf_processor import process_pdf, detect_ocr_pdf
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.schema import Document

logger = logging.getLogger(__name__)


class RobustPDFProcessor:
    """Enhanced PDF processor with robust error handling"""

    def __init__(self):
        self.max_retries = 3
        self.fallback_strategies = [
            'pymupdf_standard',
            'pymupdf_ocr',
            'langchain_fallback'
        ]
        self.validation_thresholds = {
            'min_text_length': 100,
            'min_word_count': 20,
            'min_printable_ratio': 0.8,
            'max_file_size_mb': int(os.getenv('MAX_PDF_SIZE_MB', '100')),
            'min_file_size_bytes': 1024
        }

    @performance_monitor(track_memory=True, track_cpu=True, log_parameters=True)
    def process_pdf_robust(self, pdf_path: str, **kwargs) -> Dict:
        """
        Process PDF with comprehensive error handling and fallback strategies

        Args:
            pdf_path: Path to the PDF file
            **kwargs: Additional processing parameters

        Returns:
            Dictionary with processing results
        """
        errors = []

        for attempt in range(self.max_retries):
            for strategy in self.fallback_strategies:
                try:
                    logger.info(f"Attempt {attempt + 1}, Strategy: {strategy}")

                    # Validate PDF before processing
                    validation_result = self._validate_pdf_comprehensive(pdf_path)
                    if not validation_result['valid']:
                        raise ValueError(f"PDF validation failed: {validation_result['error']}")

                    # Process based on strategy
                    if strategy == 'pymupdf_standard':
                        result = self._process_with_pymupdf(pdf_path, **kwargs)
                    elif strategy == 'pymupdf_ocr':
                        result = self._process_with_ocr(pdf_path, **kwargs)
                    else:
                        result = self._process_with_langchain_fallback(pdf_path, **kwargs)

                    # Validate result quality
                    if self._validate_extraction_quality(result):
                        logger.info(f"Successfully processed with strategy: {strategy}")
                        result['metadata']['processing_strategy'] = strategy
                        result['metadata']['attempts'] = attempt + 1
                        result['metadata']['robust_processing'] = True
                        return result
                    else:
                        raise ValueError("Extraction quality below threshold")

                except Exception as e:
                    error_msg = f"Strategy {strategy}, Attempt {attempt + 1}: {str(e)}"
                    errors.append(error_msg)
                    logger.warning(error_msg)

                    # Wait before retry with exponential backoff
                    if attempt < self.max_retries - 1:
                        wait_time = 2 ** attempt
                        logger.info(f"Waiting {wait_time} seconds before retry...")
                        time.sleep(wait_time)

        # All strategies failed
        error_summary = f"All processing strategies failed. Errors: {'; '.join(errors)}"
        logger.error(error_summary)
        raise Exception(error_summary)

    def _validate_pdf_comprehensive(self, pdf_path: str) -> Dict:
        """Comprehensive PDF validation"""
        try:
            # File existence and basic checks
            if not os.path.exists(pdf_path):
                return {'valid': False, 'error': 'File does not exist'}

            file_size = os.path.getsize(pdf_path)
            max_size = self.validation_thresholds['max_file_size_mb'] * 1024 * 1024

            if file_size > max_size:
                return {
                    'valid': False,
                    'error': f'File too large: {file_size / 1024 / 1024:.1f}MB > {max_size / 1024 / 1024}MB'
                }

            if file_size < self.validation_thresholds['min_file_size_bytes']:
                return {'valid': False, 'error': 'File too small, likely corrupted'}

            # PDF structure validation
            doc = fitz.open(pdf_path)

            if len(doc) == 0:
                doc.close()
                return {'valid': False, 'error': 'PDF has no pages'}

            # Test if we can extract any text from first page
            first_page = doc[0]
            test_text = first_page.get_text()

            # Check if PDF is password protected
            if doc.needs_pass:
                doc.close()
                return {'valid': False, 'error': 'PDF is password protected'}

            doc.close()

            return {
                'valid': True,
                'pages': len(doc),
                'has_text': len(test_text.strip()) > 0,
                'file_size_mb': file_size / 1024 / 1024,
                'estimated_text_length': len(test_text)
            }

        except Exception as e:
            return {'valid': False, 'error': f'Validation error: {str(e)}'}

    def _validate_extraction_quality(self, result: Dict) -> bool:
        """Validate the quality of extracted content"""
        try:
            # Check if we extracted meaningful text
            text_pages = result.get('text', [])
            if not text_pages:
                logger.warning("No text pages extracted")
                return False

            # Combine all text
            total_text = ' '.join([
                page.get('text', '') if isinstance(page, dict) else str(page)
                for page in text_pages
            ])

            # Apply quality thresholds
            if len(total_text.strip()) < self.validation_thresholds['min_text_length']:
                logger.warning(f"Text too short: {len(total_text)} < {self.validation_thresholds['min_text_length']}")
                return False

            word_count = len(total_text.split())
            if word_count < self.validation_thresholds['min_word_count']:
                logger.warning(f"Word count too low: {word_count} < {self.validation_thresholds['min_word_count']}")
                return False

            # Check for reasonable text-to-noise ratio
            printable_chars = sum(1 for c in total_text if c.isprintable())
            if len(total_text) > 0:
                printable_ratio = printable_chars / len(total_text)
                if printable_ratio < self.validation_thresholds['min_printable_ratio']:
                    logger.warning(f"Printable ratio too low: {printable_ratio:.2f} < {self.validation_thresholds['min_printable_ratio']}")
                    return False

            logger.info(f"Extraction quality validated: {len(total_text)} chars, {word_count} words, {printable_ratio:.2f} printable ratio")
            return True

        except Exception as e:
            logger.error(f"Error validating extraction quality: {e}")
            return False

    def _process_with_pymupdf(self, pdf_path: str, **kwargs) -> Dict:
        """Process PDF using standard PyMuPDF method"""
        try:
            logger.info("Processing with standard PyMuPDF method")
            result = process_pdf(pdf_path, **kwargs)

            if not result or not result.get('text'):
                raise ValueError("No text extracted with standard PyMuPDF")

            return result

        except Exception as e:
            logger.error(f"PyMuPDF standard processing failed: {e}")
            raise

    def _process_with_ocr(self, pdf_path: str, **kwargs) -> Dict:
        """Process PDF using OCR-specific method"""
        try:
            logger.info("Processing with OCR-specific method")

            # First detect if it's an OCR PDF
            ocr_detection = detect_ocr_pdf(pdf_path)

            if not ocr_detection.get('is_ocr_pdf', False):
                # Force OCR processing for this fallback
                logger.info("Forcing OCR processing as fallback strategy")

            # Use OCR-specific processing
            result = process_pdf(pdf_path, **kwargs)

            if not result or not result.get('text'):
                raise ValueError("No text extracted with OCR method")

            # Add OCR metadata
            result['metadata']['ocr_fallback'] = True
            result['metadata']['ocr_confidence'] = ocr_detection.get('confidence', 0.0)

            return result

        except Exception as e:
            logger.error(f"OCR processing failed: {e}")
            raise

    def _process_with_langchain_fallback(self, pdf_path: str, **kwargs) -> Dict:
        """Process PDF using LangChain as ultimate fallback"""
        try:
            logger.info("Processing with LangChain fallback method")

            # Use basic text extraction
            doc = fitz.open(pdf_path)
            text_pages = []

            for page_num in range(len(doc)):
                page = doc[page_num]
                text = page.get_text()

                if text.strip():
                    text_pages.append({
                        'page': page_num + 1,
                        'text': text,
                        'extraction_method': 'langchain_fallback'
                    })

            doc.close()

            if not text_pages:
                raise ValueError("No text extracted with LangChain fallback")

            # Create minimal result structure
            result = {
                'text': text_pages,
                'images': [],
                'tables': [],
                'links': [],
                'metadata': {
                    'page_count': len(text_pages),
                    'image_count': 0,
                    'table_count': 0,
                    'link_count': 0,
                    'extraction_method': 'langchain_fallback',
                    'fallback_processing': True
                }
            }

            return result

        except Exception as e:
            logger.error(f"LangChain fallback processing failed: {e}")
            raise

    def get_processing_statistics(self) -> Dict:
        """Get statistics about processing attempts and success rates"""
        # This could be enhanced to track statistics across multiple processing attempts
        return {
            'max_retries': self.max_retries,
            'available_strategies': self.fallback_strategies,
            'validation_thresholds': self.validation_thresholds
        }