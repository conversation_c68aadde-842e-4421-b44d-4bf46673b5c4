import os
import logging
import json
from datetime import datetime
from werkzeug.utils import secure_filename
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.schema import Document

from app.services.vector_db import get_vector_db, add_documents_with_category
from scripts.setup.create_temp_dirs import create_pdf_directory_structure
from app.utils.pdf_db import process_pdf_db_first
from app.utils import content_db as db
from app.services.embedding_service import scrape_url
from app.services.enhanced_chunking_service import EnhancedChunkingService
from app.services.document_structure_preserver import DocumentStructurePreserver
from app.services.robust_pdf_processor import RobustPDFProcessor
from app.services.adaptive_chunking_optimizer import AdaptiveChunkingOptimizer
from app.utils.performance_monitor import performance_monitor

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

TEMP_FOLDER = os.getenv("TEMP_FOLDER", "./data/temp")

@performance_monitor(track_memory=True, track_cpu=True, log_parameters=True)
def embed_file_db_first(file, category, source_url=None, use_vision=None, filter_sensitivity=None, max_images=None, force_update=False, original_filename=None, form_id=None, convert_to_non_ocr=False, keep_only_non_ocr=False):
    """
    Embed a PDF file into the vector database using the database-first retrieval approach.

    This function prioritizes database queries over network requests for better performance.

    Args:
        file: The uploaded file object
        category: The category for organizing content
        source_url: Original URL where the PDF was obtained
        use_vision: Whether to use vision model for image analysis
        filter_sensitivity: Sensitivity level for filtering images
        max_images: Maximum number of images to save
        force_update: Force update of URL content even if it's fresh
        original_filename: Original filename to use (for file replacement scenarios)
        form_id: Form ID for gated PDFs (optional)
        convert_to_non_ocr: Whether to convert the PDF to a non-OCR format
        keep_only_non_ocr: Whether to keep only the non-OCR version of the PDF

    Returns:
        tuple: (success, message)
    """
    try:
        if not file or not category:
            return False, "File and category are required"

        # Always use the user-uploaded filename for original_filename
        user_original_filename = secure_filename(file.filename)

        # --- ALWAYS DELETE OLD RECORDS FOR THIS original_filename AND CATEGORY ---
        try:
            from app.utils.content_db import get_db_connection
            with get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    DELETE FROM pdf_documents
                    WHERE original_filename = ? AND category = ?
                ''', (user_original_filename, category))
                conn.commit()
                logger.info(f"Deleted all previous records for {user_original_filename} in category {category} (enforced replace mode)")
        except Exception as replace_exc:
            logger.error(f"Error deleting old records for enforced replace: {replace_exc}")
        # --- END ALWAYS DELETE LOGIC ---

        # Use clean filename approach: always use the original filename without timestamps
        if original_filename:
            filename = original_filename
            logger.info(f"Using provided filename for replacement: {filename}")
        else:
            filename = user_original_filename  # Use clean filename instead of timestamped
            logger.info(f"Using clean filename for new upload: {filename}")
        
        file_original_name = user_original_filename

        # Create the directory structure for this PDF
        dir_structure = create_pdf_directory_structure(category, filename)
        if not dir_structure:
            logger.error(f"Failed to create directory structure for {filename}")
            return False, f"Failed to create directory structure for {filename}"

        # Get the path to save the PDF
        dest = dir_structure["pdf_path"]
        logger.info(f"PDF will be saved to: {dest}")

        # Save the uploaded file
        logger.info(f"Saving uploaded file to: {dest}")
        logger.info(f"File object type: {type(file)}")
        logger.info(f"File object filename: {file.filename}")
        logger.info(f"File object content length: {file.content_length if hasattr(file, 'content_length') else 'Unknown'}")
        logger.info(f"File object has save method: {hasattr(file, 'save')}")
        logger.info(f"File object has file_path attribute: {hasattr(file, 'file_path')}")

        # Check if file object has been read/consumed
        if hasattr(file, 'tell') and hasattr(file, 'seek'):
            current_pos = file.tell()
            logger.info(f"File object current position: {current_pos}")
            if current_pos > 0:
                logger.info("File object has been read, seeking to beginning")
                file.seek(0)
            # Always ensure we're at the beginning, even if position was 0
            file.seek(0)
            logger.info(f"File object position after seek(0): {file.tell()}")

        # Ensure destination directory exists
        dest_dir = os.path.dirname(dest)
        if not os.path.exists(dest_dir):
            logger.info(f"Creating destination directory: {dest_dir}")
            os.makedirs(dest_dir, exist_ok=True)

        try:
            file.save(dest)
            logger.info(f"File save operation completed")
        except Exception as save_error:
            logger.error(f"File save operation failed: {str(save_error)}")
            return False, f"File save failed: {str(save_error)}"

        logger.info(f"File saved successfully. File exists: {os.path.exists(dest)}")

        # Extra check: If file does not exist after save, log and abort
        if not os.path.exists(dest):
            logger.error(f"ERROR: File was not saved successfully to {dest} (file missing after save)")
            # List files in the directory to help debug
            if os.path.exists(dest_dir):
                files_in_dir = os.listdir(dest_dir)
                logger.error(f"Files in destination directory {dest_dir}: {files_in_dir}")
            return False, f"File save failed: {dest} does not exist after save."

        # Check file size after saving
        file_size = os.path.getsize(dest)
        logger.info(f"Saved file size: {file_size} bytes")
        if file_size == 0:
            logger.error(f"WARNING: Saved file is empty! File size: {file_size} bytes")
            return False, f"File save failed: saved file is empty (0 bytes)"

        # Process the PDF with database-first approach
        logger.info(f"Processing PDF {file.filename} with database-first approach...")
        logger.info(f"PDF path for processing: {dest}")
        logger.info(f"PDF file exists before processing: {os.path.exists(dest)}")
        
        if use_vision:
            logger.info(f"Vision model analysis enabled with sensitivity: {filter_sensitivity}, max images: {max_images}")
        else:
            logger.info("Vision model analysis disabled")

        # Initialize enhanced services
        robust_processor = RobustPDFProcessor()
        chunking_service = EnhancedChunkingService()
        structure_preserver = DocumentStructurePreserver()
        chunking_optimizer = AdaptiveChunkingOptimizer()

        # Process the PDF using robust processing with fallback strategies
        try:
            pdf_info = robust_processor.process_pdf_robust(
                dest,
                category=category,
                source_url=source_url,
                use_vision=use_vision,
                filter_sensitivity=filter_sensitivity,
                max_images=max_images,
                extract_locations=True
            )
        except Exception as robust_error:
            logger.warning(f"Robust processing failed, falling back to standard processing: {robust_error}")
            # Fallback to original processing
            pdf_info = process_pdf_db_first(
                dest,
                category,
                source_url,
                use_vision=use_vision,
                filter_sensitivity=filter_sensitivity,
                max_images=max_images,
                force_update=force_update,
                extract_locations=True
            )

        # Debug: Log the PDF processing results
        logger.info(f"PDF processing results for {file.filename}:")
        logger.info(f"  Text pages: {len(pdf_info.get('text', []))}")
        logger.info(f"  Images: {len(pdf_info.get('images', []))}")
        logger.info(f"  Tables: {len(pdf_info.get('tables', []))}")
        logger.info(f"  Links: {len(pdf_info.get('links', []))}")
        logger.info(f"  Metadata: {pdf_info.get('metadata', {})}")
        
        if not pdf_info.get('text'):
            logger.error(f"No text extracted from PDF {file.filename}")
            logger.error(f"PDF info keys: {list(pdf_info.keys())}")
            if 'metadata' in pdf_info:
                logger.error(f"PDF metadata: {pdf_info['metadata']}")

        # Extract articles metadata (list of dicts with page, title, authors)
        articles = pdf_info.get("metadata", {}).get("articles", [])
        from collections import defaultdict
        articles_by_page = defaultdict(list)
        for art in articles:
            articles_by_page[art["page"]].append(art)

        # Convert the processed PDF to document chunks for vector storage
        chunks = []

        if pdf_info["text"]:
            # Create documents from the processed PDF
            documents = []
            for page in pdf_info["text"]:
                page_num = page["page"]
                page_text = page["text"]

                # Create metadata
                metadata = {
                    "source": filename,
                    "original_filename": user_original_filename,
                    "citation_filename": user_original_filename,  # Add this field specifically for citations
                    "page": page_num,
                    "type": "pdf",
                    "extraction_method": page.get("extraction_method", "standard")
                }

                # Add source URL if provided
                if source_url:
                    metadata["original_url"] = source_url

                # Add category
                metadata["category"] = category

                # Add database retrieval info
                if pdf_info["metadata"].get("database_retrieval"):
                    metadata["database_retrieval"] = True
                    metadata["url_last_scraped"] = pdf_info["metadata"].get("url_last_scraped")

                # Add source URL ID if available
                if pdf_info["metadata"].get("source_url_id"):
                    metadata["source_url_id"] = pdf_info["metadata"].get("source_url_id")

                # Add PDF document ID if available
                if pdf_info["metadata"].get("pdf_document_id"):
                    metadata["pdf_document_id"] = pdf_info["metadata"].get("pdf_document_id")

                # Add cover image ID if available
                if pdf_info["metadata"].get("cover_image_id"):
                    metadata["cover_image_id"] = pdf_info["metadata"].get("cover_image_id")

                # Add images for this page
                page_images = [img for img in pdf_info["images"] if img.get("page") == page_num]
                if page_images:
                    metadata["images"] = json.dumps(page_images)
                    metadata["image_count"] = len(page_images)

                # Add links
                if pdf_info["links"]:
                    metadata["pdf_links"] = json.dumps(pdf_info["links"])
                    metadata["link_count"] = len(pdf_info["links"])

                # Add form ID if available
                if form_id:
                    metadata["form_id"] = form_id

                # Add article title/authors for this page if available
                page_articles = articles_by_page.get(page_num, [])
                if page_articles:
                    metadata["article_title"] = page_articles[0]["title"]
                    metadata["article_authors"] = page_articles[0]["authors"]

                # Create document
                documents.append(Document(page_content=page_text, metadata=metadata))

            # Apply enhanced chunking with content-type detection and optimization
            logger.info(f"Applying enhanced chunking to {len(documents)} documents")

            # Detect content type from sample text
            sample_text = ' '.join([doc.page_content[:500] for doc in documents[:3]])
            content_type = chunking_service.content_detector.detect_content_type(sample_text)
            logger.info(f"Detected content type: {content_type}")

            # Optimize chunking configuration for this specific document
            optimized_config = chunking_optimizer.optimize_for_document(
                dest, {'content_type': content_type}
            )

            # Apply adaptive chunking with optimized configuration
            # Update the chunking service configuration
            chunking_service.config.content_type_configs[content_type] = optimized_config
            chunks = chunking_service.adaptive_chunk(documents, content_type)

            # Enhance chunks with structure preservation
            enhanced_chunks = []
            document_context = {
                'total_pages': len(pdf_info.get('text', [])),
                'document_type': content_type,
                'language': 'en'  # Could be enhanced with language detection
            }

            for chunk in chunks:
                enhanced_chunk = structure_preserver.enhance_chunk_metadata(
                    chunk,
                    page_structure=pdf_info.get('metadata', {}),
                    document_context=document_context
                )
                enhanced_chunks.append(enhanced_chunk)

            chunks = enhanced_chunks
            logger.info(f"Enhanced chunking created {len(chunks)} chunks with structure preservation")

            # Ensure all chunks have the source filename and URL if provided
            for doc in chunks:
                doc.metadata["source"] = filename
                doc.metadata["original_filename"] = user_original_filename
                doc.metadata["citation_filename"] = user_original_filename
                doc.metadata["type"] = "pdf"
                if source_url:
                    doc.metadata["original_url"] = source_url
                if form_id:
                    doc.metadata["form_id"] = form_id

            # Ensure all chunks have the cover image metadata if available
            thumb_meta = pdf_info.get("metadata", {})
            for doc in chunks:
                doc.metadata["thumbnail_path"] = thumb_meta.get("thumbnail_path", "")
                doc.metadata["thumbnail_url"] = thumb_meta.get("thumbnail_url", "")
                doc.metadata["thumbnail_source"] = thumb_meta.get("thumbnail_source", "")
                doc.metadata["thumbnail_description"] = thumb_meta.get("thumbnail_description", "")

        if not chunks:
            return False, f"Failed to extract text from {file.filename}"

        # Add to vector database using category-aware method
        add_documents_with_category(chunks, category)

        # Get metadata from the processed PDF
        image_count = pdf_info["metadata"]["image_count"]
        table_count = pdf_info["metadata"]["table_count"]
        link_count = pdf_info["metadata"]["link_count"]

        # Add vision analysis metadata to success message
        images_filtered = pdf_info["metadata"].get("images_filtered", 0)
        vision_enabled = pdf_info["metadata"].get("vision_enabled", False)

        # Add the PDF directory path to the metadata for all chunks
        pdf_base_name = os.path.splitext(filename)[0]
        pdf_dir_path = f"/{category}/{pdf_base_name}"

        # Build success message
        success_message = f"Successfully embedded {file.filename} with {len(chunks)} chunks"
        if image_count > 0:
            success_message += f", {image_count} images"
            # Add vision analysis details if enabled
            if vision_enabled:
                success_message += f" ({images_filtered} filtered by vision model)"
        if table_count > 0:
            success_message += f", {table_count} tables"
        if link_count > 0:
            success_message += f", {link_count} links"

        # Add database retrieval info to success message
        if pdf_info["metadata"].get("database_retrieval"):
            success_message += " (used database content for URL)"

        logger.info(success_message)
        if source_url:
            logger.info(f"PDF {file.filename} linked to source URL: {source_url}")

        # --- DELETE OLD RECORDS LOGIC ---
        try:
            import sqlite3
            from app.utils.content_db import get_db_connection
            # Use the original filename, category, and form_id to find duplicates
            with get_db_connection() as conn:
                cursor = conn.cursor()
                # Find all records with the same original_filename, category, and form_id, ordered by created_at DESC
                cursor.execute('''
                    SELECT id FROM pdf_documents
                    WHERE original_filename = ? AND category = ? AND form_id IS ?
                    ORDER BY created_at DESC
                ''', (user_original_filename, category, form_id))
                rows = cursor.fetchall()
                # Keep the most recent (first), delete the rest
                if rows and len(rows) > 1:
                    ids_to_delete = [row[0] for row in rows[1:]]
                    cursor.executemany('DELETE FROM pdf_documents WHERE id = ?', [(i,) for i in ids_to_delete])
                    conn.commit()
                    logger.info(f"Deleted {len(ids_to_delete)} old pdf_documents records for {user_original_filename}, category {category}, form_id {form_id}")
        except Exception as cleanup_exc:
            logger.error(f"Error cleaning up old pdf_documents records: {cleanup_exc}")
        # --- END DELETE OLD RECORDS LOGIC ---

        return True, success_message
    except Exception as e:
        logger.error(f"Failed to embed file {file.filename}: {str(e)}")
        return False, f"Failed to embed {file.filename}: {str(e)}"

def scrape_url_db_first(url, category, depth=0, force_update=False):
    """
    Scrape a URL with database-first retrieval approach.

    This function checks if URL content exists in the database before scraping.
    It prioritizes database queries over network requests for better performance.

    Args:
        url (str): The URL to scrape
        category (str): The category for organizing content
        depth (int): The depth of links to follow (0-3)
        force_update (bool): Force update of URL content even if it's fresh

    Returns:
        tuple: (success, message, data)
    """
    try:
        if not url or not category:
            return False, "URL and category are required", None

        # Limit depth to a reasonable range
        if depth < 0:
            depth = 0
        if depth > 3:
            depth = 3

        # Check if URL exists in database
        url_record = db.get_source_url_by_url(url)
        url_content_retrieved = False
        scraped_data = None

        # Determine if we should use database content or scrape
        if url_record and db.is_url_content_fresh(url_record) and not force_update:
            logger.info(f"Using fresh database content for URL: {url}")
            source_url_id = url_record['id']

            # Get URL content from database
            url_images = db.get_url_images(source_url_id)
            url_links = db.get_url_links(source_url_id)
            url_text = db.get_url_text(source_url_id)

            # Process images to ensure they're valid strings
            processed_images = []
            for img in url_images:
                img_url = img['url']
                if isinstance(img_url, str) and img_url.startswith(('http://', 'https://')):
                    processed_images.append(img_url)
                else:
                    logger.warning(f"Skipping invalid image URL: {img_url}")

            # Process links to ensure they're valid strings
            processed_links = []
            for link in url_links:
                link_url = link['url']
                if isinstance(link_url, str) and link_url.startswith(('http://', 'https://')):
                    processed_links.append(link_url)
                else:
                    logger.warning(f"Skipping invalid link URL: {link_url}")

            # Create scraped data structure
            scraped_data = {
                "url": url,
                "text": url_text,
                "images": processed_images,
                "links": processed_links,
                "pages": [{
                    "url": url,
                    "text": url_text,
                    "images": processed_images,
                    "links": processed_links,
                    "depth": 0
                }],
                "pages_scraped": 1,
                "max_depth_reached": 0,
                "database_retrieval": True,
                "url_last_scraped": url_record['last_scraped']
            }

            url_content_retrieved = True
        else:
            # Need to scrape the URL
            logger.info(f"Scraping URL content for: {url} with depth {depth}")

            # Use the original scrape_url function
            scraped_data = scrape_url(url, depth)

            if scraped_data and not scraped_data.get("error"):
                # Get title and description if available
                title = None
                description = None

                # Try to extract title and description from the first page
                if scraped_data.get("pages") and len(scraped_data["pages"]) > 0:
                    first_page = scraped_data["pages"][0]

                    # Extract title from the first line if possible
                    text_lines = first_page.get("text", "").split('\n')
                    if text_lines and len(text_lines) > 0:
                        title = text_lines[0][:200]  # Limit title length

                    # Use the first paragraph as description
                    if len(text_lines) > 1:
                        description = text_lines[1][:500]  # Limit description length

                # Insert or update source URL record
                source_url_id = db.insert_source_url(
                    url,
                    title=title,
                    description=description,
                    status='active'
                )

                if source_url_id:
                    # Process each page
                    for i, page in enumerate(scraped_data.get("pages", [])):
                        page_url = page.get("url", url)
                        page_text = page.get("text", "")
                        page_images = page.get("images", [])
                        page_links = page.get("links", [])

                        # Store text content
                        if page_text:
                            # Split text into manageable chunks if it's very large
                            max_chunk_size = 10000  # Characters per chunk
                            if len(page_text) > max_chunk_size:
                                chunks = [page_text[i:i+max_chunk_size]
                                         for i in range(0, len(page_text), max_chunk_size)]
                                for j, chunk in enumerate(chunks):
                                    db.insert_url_content(source_url_id, 'text', chunk, i * 100 + j)
                            else:
                                db.insert_url_content(source_url_id, 'text', page_text, i * 100)

                        # Store images
                        for j, img_url in enumerate(page_images):
                            # Validate that img_url is a string, not a dictionary
                            if isinstance(img_url, dict):
                                if 'url' in img_url:
                                    actual_url = img_url['url']
                                    logger.warning(f"Found dictionary instead of URL string for image, extracting URL: {actual_url}")
                                    img_url = actual_url
                                else:
                                    logger.error(f"Invalid image data (dictionary without 'url' key): {img_url}")
                                    continue
                            elif not isinstance(img_url, str):
                                logger.error(f"Invalid image URL type: {type(img_url)} - {img_url}")
                                continue

                            # Ensure it's a valid URL
                            if not img_url.startswith(('http://', 'https://')):
                                logger.warning(f"Skipping invalid image URL (missing protocol): {img_url}")
                                continue

                            metadata = {
                                "page_url": page_url,
                                "page_index": i,
                                "index": j,
                                "source": "url_scrape"
                            }
                            db.insert_url_content(source_url_id, 'image', img_url, i * 100 + j, metadata)

                        # Store links
                        for j, link_url in enumerate(page_links):
                            # Validate that link_url is a string, not a dictionary
                            if isinstance(link_url, dict):
                                if 'url' in link_url:
                                    actual_url = link_url['url']
                                    logger.warning(f"Found dictionary instead of URL string for link, extracting URL: {actual_url}")
                                    link_url = actual_url
                                else:
                                    logger.error(f"Invalid link data (dictionary without 'url' key): {link_url}")
                                    continue
                            elif not isinstance(link_url, str):
                                logger.error(f"Invalid link URL type: {type(link_url)} - {link_url}")
                                continue

                            # Ensure it's a valid URL
                            if not link_url.startswith(('http://', 'https://')):
                                logger.warning(f"Skipping invalid link URL (missing protocol): {link_url}")
                                continue

                            metadata = {
                                "page_url": page_url,
                                "page_index": i,
                                "index": j,
                                "source": "url_scrape"
                            }
                            db.insert_url_content(source_url_id, 'link', link_url, i * 100 + j, metadata)

                    # Add database info to scraped data
                    scraped_data["database_retrieval"] = False
                    scraped_data["source_url_id"] = source_url_id

                    url_content_retrieved = True
                else:
                    logger.error(f"Failed to insert source URL record for {url}")
            else:
                error_msg = scraped_data.get("error", "Unknown error") if scraped_data else "Failed to scrape URL"
                logger.error(f"Failed to scrape URL {url}: {error_msg}")

                # Record the error in the database
                source_url_id = db.insert_source_url(
                    url,
                    status='error',
                    error_message=error_msg
                )

                return False, f"Failed to scrape URL {url}: {error_msg}", None

        if not url_content_retrieved or not scraped_data:
            return False, f"Failed to retrieve content for URL {url}", None

        return True, "URL content retrieved successfully", scraped_data
    except Exception as e:
        logger.error(f"Error processing URL {url}: {str(e)}")
        return False, f"Error processing URL {url}: {str(e)}", None
