"""
Document Structure Preservation Service

This module provides functionality to preserve and enhance document structure
information during the chunking process, maintaining document hierarchy,
reading order, and semantic density.
"""

import re
import logging
from typing import Dict, List, Any, Optional, Tuple
from langchain.schema import Document
import spacy
from collections import defaultdict

logger = logging.getLogger(__name__)

# Try to load spaCy model for better text analysis
try:
    nlp = spacy.load("en_core_web_sm")
except OSError:
    logger.warning("spaCy English model not found. Some features may be limited.")
    nlp = None


class DocumentStructurePreserver:
    """Preserve document structure during chunking"""

    def __init__(self):
        self.structure_patterns = {
            'headers': [
                r'^#{1,6}\s+(.+)$',  # Markdown headers
                r'^([A-Z][A-Z\s]{2,})\s*$',  # ALL CAPS headers
                r'^\d+\.\s+(.+)$',  # Numbered sections
                r'^([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)\s*$',  # Title case headers
                r'^([IVX]+)\.\s+(.+)$',  # Roman numeral sections
            ],
            'lists': [
                r'^\s*[-*+•]\s+(.+)$',  # Bullet points
                r'^\s*\d+\.\s+(.+)$',  # Numbered lists
                r'^\s*[a-z]\)\s+(.+)$',  # Lettered lists
                r'^\s*\([a-z]\)\s+(.+)$',  # Parenthetical lettered lists
            ],
            'tables': [
                r'\|.*\|',  # Table rows with pipes
                r'^[\s\-\|]+$',  # Table separators
                r'^\s*\+[-=]+\+',  # ASCII table borders
                r'^\s*[A-Za-z0-9\s]+\s+[A-Za-z0-9\s]+\s+[A-Za-z0-9\s]+',  # Space-separated columns
            ],
            'citations': [
                r'\[\d+\]',  # Numbered citations [1]
                r'\([A-Za-z]+\s+\d{4}\)',  # Author year citations (Smith 2020)
                r'\([A-Za-z]+\s+et\s+al\.\s+\d{4}\)',  # Et al citations
            ],
            'equations': [
                r'\$.*\$',  # LaTeX equations
                r'^\s*\([0-9]+\)\s*$',  # Equation numbers
                r'[=<>≤≥≠±∑∫∂∇]',  # Mathematical symbols
            ]
        }

        # Semantic density keywords for different content types
        self.semantic_keywords = {
            'technical': ['algorithm', 'method', 'implementation', 'system', 'framework', 'architecture'],
            'scientific': ['hypothesis', 'experiment', 'results', 'analysis', 'conclusion', 'methodology'],
            'narrative': ['story', 'character', 'plot', 'setting', 'theme', 'narrative'],
            'academic': ['research', 'study', 'findings', 'literature', 'theory', 'evidence']
        }

    def enhance_chunk_metadata(self, chunk: Document, page_structure: Dict = None,
                             document_context: Dict = None) -> Document:
        """
        Enhance chunk with comprehensive structural metadata

        Args:
            chunk: Document chunk to enhance
            page_structure: Structure information from the page
            document_context: Overall document context information

        Returns:
            Enhanced Document with structural metadata
        """
        try:
            # Analyze structural elements in chunk
            structure_info = self._analyze_chunk_structure(chunk.page_content)

            # Calculate semantic density
            semantic_density = self._calculate_semantic_density(chunk.page_content)

            # Determine section hierarchy
            section_hierarchy = self._determine_section_hierarchy(chunk.page_content)

            # Calculate reading order score
            reading_order = self._calculate_reading_order(chunk, page_structure or {})

            # Detect content type
            content_type = self._detect_content_type(chunk.page_content)

            # Calculate chunk quality score
            quality_score = self._calculate_chunk_quality(chunk.page_content, structure_info)

            # Add enhanced metadata
            enhanced_metadata = chunk.metadata.copy()
            enhanced_metadata.update({
                # Structure information
                'has_headers': len(structure_info['headers']) > 0,
                'header_count': len(structure_info['headers']),
                'headers': structure_info['headers'][:3],  # Limit to first 3 headers
                'has_lists': len(structure_info['lists']) > 0,
                'list_count': len(structure_info['lists']),
                'has_tables': len(structure_info['tables']) > 0,
                'table_count': len(structure_info['tables']),
                'has_citations': len(structure_info['citations']) > 0,
                'citation_count': len(structure_info['citations']),
                'has_equations': len(structure_info['equations']) > 0,
                'equation_count': len(structure_info['equations']),

                # Hierarchy and organization
                'section_hierarchy': section_hierarchy,
                'reading_order_score': reading_order,
                'content_type_detected': content_type,

                # Quality metrics
                'semantic_density': semantic_density,
                'chunk_quality_score': quality_score,
                'text_length': len(chunk.page_content),
                'word_count': len(chunk.page_content.split()),
                'sentence_count': len([s for s in chunk.page_content.split('.') if s.strip()]),

                # Processing metadata
                'structure_enhanced': True,
                'enhancement_version': '1.0'
            })

            # Add document context if available
            if document_context:
                enhanced_metadata.update({
                    'document_total_pages': document_context.get('total_pages', 0),
                    'document_type': document_context.get('document_type', 'unknown'),
                    'document_language': document_context.get('language', 'en')
                })

            # Create enhanced document
            enhanced_chunk = Document(
                page_content=chunk.page_content,
                metadata=enhanced_metadata
            )

            return enhanced_chunk

        except Exception as e:
            logger.error(f"Error enhancing chunk metadata: {e}")
            # Return original chunk if enhancement fails
            return chunk

    def _analyze_chunk_structure(self, text: str) -> Dict[str, List[str]]:
        """Analyze structural elements in text"""
        structure = {
            'headers': [],
            'lists': [],
            'tables': [],
            'citations': [],
            'equations': []
        }

        lines = text.split('\n')
        for line in lines:
            line_stripped = line.strip()
            if not line_stripped:
                continue

            # Check each structure type
            for element_type, patterns in self.structure_patterns.items():
                for pattern in patterns:
                    if re.search(pattern, line_stripped, re.IGNORECASE):
                        structure[element_type].append(line_stripped[:100])  # Limit length
                        break

        return structure

    def _calculate_semantic_density(self, text: str) -> float:
        """Calculate semantic density of the text"""
        try:
            if not text.strip():
                return 0.0

            words = text.lower().split()
            if not words:
                return 0.0

            # Count semantic keywords
            semantic_word_count = 0
            for category, keywords in self.semantic_keywords.items():
                for keyword in keywords:
                    semantic_word_count += text.lower().count(keyword)

            # Calculate density as ratio of semantic words to total words
            density = min(semantic_word_count / len(words), 1.0)

            # Boost density for technical terms and proper nouns
            if nlp:
                doc = nlp(text[:1000])  # Limit text for performance
                technical_terms = sum(1 for token in doc if token.pos_ in ['NOUN', 'PROPN'] and len(token.text) > 4)
                density += min(technical_terms / len(words), 0.3)

            return min(density, 1.0)

        except Exception as e:
            logger.error(f"Error calculating semantic density: {e}")
            return 0.5  # Default moderate density

    def _determine_section_hierarchy(self, text: str) -> Dict[str, Any]:
        """Determine the hierarchical structure of the text"""
        hierarchy = {
            'level': 0,
            'section_type': 'content',
            'parent_section': None,
            'subsections': []
        }

        try:
            lines = text.split('\n')

            # Look for header patterns to determine hierarchy level
            for line in lines:
                line_stripped = line.strip()

                # Check for markdown headers
                if line_stripped.startswith('#'):
                    hierarchy['level'] = line_stripped.count('#')
                    hierarchy['section_type'] = 'header'
                    hierarchy['parent_section'] = line_stripped.lstrip('#').strip()
                    break

                # Check for numbered sections
                numbered_match = re.match(r'^(\d+)\.', line_stripped)
                if numbered_match:
                    hierarchy['level'] = len(numbered_match.group(1))
                    hierarchy['section_type'] = 'numbered_section'
                    hierarchy['parent_section'] = line_stripped
                    break

                # Check for ALL CAPS headers
                if line_stripped.isupper() and len(line_stripped) > 3:
                    hierarchy['level'] = 1
                    hierarchy['section_type'] = 'major_header'
                    hierarchy['parent_section'] = line_stripped
                    break

            return hierarchy

        except Exception as e:
            logger.error(f"Error determining section hierarchy: {e}")
            return hierarchy

    def _calculate_reading_order(self, chunk: Document, page_structure: Dict) -> float:
        """Calculate reading order score based on position and structure"""
        try:
            # Base score from page number
            page_num = chunk.metadata.get('page', 1)
            base_score = page_num * 1000  # Base score from page position

            # Adjust based on position within page
            if 'position' in chunk.metadata:
                position = chunk.metadata['position']
                if isinstance(position, dict):
                    # Use y-coordinate for vertical position (higher y = earlier in reading order)
                    y_pos = position.get('y', 0)
                    base_score += (1000 - y_pos)  # Invert y-coordinate

            # Adjust based on structural elements
            if chunk.metadata.get('has_headers'):
                base_score -= 100  # Headers come first

            if chunk.metadata.get('has_lists'):
                base_score += 50  # Lists typically follow content

            # Normalize to 0-1 range
            return min(base_score / 10000, 1.0)

        except Exception as e:
            logger.error(f"Error calculating reading order: {e}")
            return 0.5  # Default middle position

    def _detect_content_type(self, text: str) -> str:
        """Detect the type of content in the text"""
        try:
            text_lower = text.lower()

            # Count keywords for each content type
            type_scores = {}
            for content_type, keywords in self.semantic_keywords.items():
                score = sum(text_lower.count(keyword) for keyword in keywords)
                type_scores[content_type] = score

            # Return the type with highest score, or 'general' if no clear winner
            if type_scores:
                max_type = max(type_scores, key=type_scores.get)
                if type_scores[max_type] > 0:
                    return max_type

            return 'general'

        except Exception as e:
            logger.error(f"Error detecting content type: {e}")
            return 'general'

    def _calculate_chunk_quality(self, text: str, structure_info: Dict) -> float:
        """Calculate overall quality score for the chunk"""
        try:
            if not text.strip():
                return 0.0

            quality_score = 0.0

            # Text length factor (optimal around 500-1000 characters)
            text_len = len(text)
            if 300 <= text_len <= 1500:
                quality_score += 0.3
            elif text_len > 100:
                quality_score += 0.1

            # Structure factor
            structure_count = sum(len(items) for items in structure_info.values())
            if structure_count > 0:
                quality_score += min(structure_count * 0.1, 0.3)

            # Coherence factor (sentences should be reasonably complete)
            sentences = [s.strip() for s in text.split('.') if s.strip()]
            complete_sentences = sum(1 for s in sentences if len(s) > 10 and s[0].isupper())
            if sentences:
                coherence = complete_sentences / len(sentences)
                quality_score += coherence * 0.4

            return min(quality_score, 1.0)

        except Exception as e:
            logger.error(f"Error calculating chunk quality: {e}")
            return 0.5  # Default moderate quality